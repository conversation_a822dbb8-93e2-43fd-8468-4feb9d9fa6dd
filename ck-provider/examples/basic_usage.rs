use ck_provider::{write_to_ck_parallel, CkConfig, Ck<PERSON>rovider, CkProviderExt, CkProviderImpl};

use std::time::Duration;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[derive(Row, Serialize, Deserialize, Debug, Clone)]
struct User {
    id: u32,
    name: String,
    age: u8,
}

use serde::{Deserialize, Serialize};
use clickhouse::Row;

/// ODS Product Configuration structure
/// Contains product information for aggregation
/// Corresponds to the Scala OdsProductConfig case class
#[derive(Debug, Clone, Deserialize, PartialEq, Row)]
#[allow(non_snake_case)]
pub struct OdsProductConfig {
    pub DATA_SOURCE: String,
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: Option<String>,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub PRODUCT: Option<String>,
    pub PRODUCT_TYPE: Option<String>,
    pub PRODUCT_FAMILY: Option<String>,
}


#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 配置ClickHouse连接 - 使用默认配置并自定义需要的字段
    let config = CkConfig {
        url: "http://mpp01.qa.guwave.com:8123".to_string(),
        username: "admin".to_string(),
        password: "admin@ck@Guwave".to_string(),
        database: "ods".to_string(),
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
        ..Default::default() // 使用默认值填充其他字段
    };

    let ck_provider = CkProviderImpl::new(config.clone());

    let product_sql = "SELECT DISTINCT DATA_SOURCE
               ,CUSTOMER
               ,SUB_CUSTOMER
               ,FACTORY
               ,FACTORY_SITE
               ,TEST_AREA
               ,TEST_STAGE
               ,DEVICE_ID
               ,PRODUCT
               ,PRODUCT_TYPE
               ,PRODUCT_FAMILY
        FROM ods.ods_yms_wafermap_config_snapshot_cluster
        WHERE DATA_SOURCE = 'Test Raw Data'
          AND CUSTOMER = 'GUWAVE'
          AND FACTORY = 'CZ_343_002'
          AND TEST_AREA = 'CP'
          AND TEST_STAGE = 'CP1'
          AND DEVICE_ID = 'GUBO_PART_TYP_001'
          AND DT = (SELECT LATEST_PARTITION_VALUE
                    FROM meta.meta_table_latest_partition_cluster
                    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'
                    AND DATABASE_NAME = 'ods')";

    let res = ck_provider.query::<OdsProductConfig>(&product_sql).await?;

    for item in res {
        println!("{:?}", item);
    }

    Ok(())
}
