use serde::{Deserialize, Serialize};
use std::sync::Arc;
use clickhouse::Row;

/// ODS Product Configuration structure
/// Contains product information for aggregation
/// Corresponds to the Scala OdsProductConfig case class
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Row)]
#[allow(non_snake_case)]
pub struct OdsProductConfig {
    pub DATA_SOURCE: String,
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: Option<String>,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub PRODUCT: Option<String>,
    pub PRODUCT_TYPE: Option<String>,
    pub PRODUCT_FAMILY: Option<String>,
}

impl OdsProductConfig {
    pub fn new() -> Self {
        Self {
            DATA_SOURCE: "".into(),
            CUSTOMER: "".into(),
            SUB_CUSTOMER: "".into(),
            FACTORY: "".into(),
            FACTORY_SITE: "".into(),
            TEST_AREA: "".into(),
            TEST_STAGE: "".into(),
            DEVICE_ID: "".into(),
            PRODUCT: "".into(),
            PRODUCT_TYPE: "".into(),
            PRODUCT_FAMILY: "".into(),
        }
    }
}

impl Default for OdsProductConfig {
    fn default() -> Self {
        Self::new()
    }
}
